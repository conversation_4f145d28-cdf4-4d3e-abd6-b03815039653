import requests
import json
import random
import string
import time
import secrets
import pya<PERSON>gui
from playwright.sync_api import sync_playwright


def load_config():
    """加载配置文件"""
    with open('config.json', 'r', encoding='utf-8') as f:
        return json.load(f)


def generate_proxy_account(base_account, minute=0, states=None, cities=None):
    """生成随机代理账户"""
    # 默认值，如果没有传入配置
    if states is None:
        states = ["california", "florida", "texas", "newyork", "georgia"]
    if cities is None:
        cities = ["losangeles", "miami", "chicago", "houston", "atlanta"]

    form = random.randint(0, 2)
    session_id = secrets.token_hex(6)

    # 构建会话时间后缀
    sess_time = f"-sessTime-{minute}" if minute > 0 else ""

    if form == 0:
        city = random.choice(cities)
        return f"{base_account}-city-{city}-session-{session_id}{sess_time}"
    elif form == 1:
        state = random.choice(states)
        return f"{base_account}-st-{state}-session-{session_id}{sess_time}"
    else:
        city = random.choice(cities)
        state = random.choice(states)
        return f"{base_account}-st-{state}-city-{city}-session-{session_id}{sess_time}"


def create_browser(url, proxy_account, proxy_password, proxy_host, proxy_port):
    """创建比特浏览器"""
    headers = {'Content-Type': 'application/json'}
    json_data = {
        "coreProduct": "chrome",
        'name': f'outlook_{int(time.time())}',
        'proxyMethod': 2,
        'proxyType': 'socks5',
        'host': proxy_host,
        'port': str(proxy_port),
        'proxyUserName': proxy_account,
        'proxyPassword': proxy_password,
        'isIpCreateTimeZone': True,
        'isIpCreatePosition': True,
        'isIpCreateLanguage': True,
        'isIpCreateDisplayLanguage': True,
        'clientRectNoiseEnabled':True,
        'browserFingerPrint': {"coreVersion": "130"}
    }

    res = requests.post(f"{url}/browser/update", data=json.dumps(json_data), headers=headers).json()
    if res['success']:
        print(f'创建浏览器成功: {res["data"]["id"]}')
        return res['data']['id']
    else:
        print(f'创建浏览器失败: {res["msg"]}')
        return None


def open_browser(url, browser_id):
    """打开浏览器"""
    headers = {'Content-Type': 'application/json'}
    json_data = {
        "id": browser_id,
        "headless": False,
        "automation": False,  # 避免“自动控制”标签造成窗口最小化
        "args": [
            "--start-maximized",         # 最大化窗口
            "--window-position=0,0",     # 设置窗口位置为左上角
            "--window-size=1280,800"     # 设置窗口大小
        ]
    }
    res = requests.post(f"{url}/browser/open", data=json.dumps(json_data), headers=headers).json()
    return res['data']['ws']


def close_browser(url, browser_id):
    """关闭浏览器"""
    headers = {'Content-Type': 'application/json'}
    json_data = {'id': browser_id}
    requests.post(f"{url}/browser/close", data=json.dumps(json_data), headers=headers)


def human_type(page, locator, text, min_delay=0.05, max_delay=0.3, timeout=None, max_retries=3):
    """模拟人工逐字符输入"""
    if timeout is None:
        config = load_config()
        timeout = config.get('timeouts', {}).get('element_wait', 90000)

    for attempt in range(max_retries):
        try:
            print(f'尝试输入到 {locator} (第{attempt + 1}次)')
            element = page.locator(locator)

            # 等待元素可见
            element.wait_for(state='visible', timeout=timeout)

            # 先清空输入框
            element.click()
            time.sleep(random.uniform(0.2, 0.5))
            element.clear()
            time.sleep(random.uniform(0.2, 0.8))

            # 逐字符输入
            for char in text:
                element.type(char)
                # 随机延迟，模拟真实打字速度
                delay = random.uniform(min_delay, max_delay)
                time.sleep(delay)

            # 输入完成后的随机停顿
            time.sleep(random.uniform(0.5, 1.5))
            print(f'成功输入到 {locator}')
            return True

        except Exception as e:
            print(f'输入失败 (第{attempt + 1}次): {str(e)}')
            if attempt < max_retries - 1:
                time.sleep(2)
            else:
                print(f'所有输入尝试都失败了: {locator}')
                return False

    return False


def human_click(page, locator, min_delay=0.5, max_delay=2.0, timeout=None, max_retries=3):
    """模拟人工点击"""
    if timeout is None:
        config = load_config()
        timeout = config.get('timeouts', {}).get('element_wait', 90000)

    for attempt in range(max_retries):
        try:
            print(f'尝试点击 {locator} (第{attempt + 1}次)')
            element = page.locator(locator)

            # 等待元素可见和可点击
            element.wait_for(state='visible', timeout=timeout)

            # 点击前的随机停顿
            time.sleep(random.uniform(0.3, 0.8))
            element.click()

            # 点击后的随机停顿
            time.sleep(random.uniform(min_delay, max_delay))
            print(f'成功点击 {locator}')
            return True

        except Exception as e:
            print(f'点击失败 (第{attempt + 1}次): {str(e)}')
            if attempt < max_retries - 1:
                time.sleep(2)
            else:
                print(f'所有点击尝试都失败了: {locator}')
                return False

    return False


def smart_click_next(page, min_delay=0.5, max_delay=2.0):
    """智能点击Next按钮，尝试多种选择器"""
    selectors = [
        'button[data-testid="primaryButton"]',  # 主要选择器
        'input[type="submit"]',                 # 备用选择器1
        'button:has-text("Next")',              # 备用选择器2
        'button:has-text("下一步")',             # 备用选择器3
        '[data-testid="primaryButton"]',        # 备用选择器4
        'button[type="submit"]'                 # 备用选择器5
    ]

    for selector in selectors:
        try:
            if human_click(page, selector, min_delay, max_delay):
                print(f"成功点击按钮: {selector}")
                return True
        except:
            continue

    print("所有按钮选择器都失败了")
    return False


def smart_type_password(page, password):
    """智能填写密码，尝试多种选择器"""
    selectors = [
        'input[type="password"][autocomplete="new-password"]',  # 主要选择器
        'input[type="password"]',                               # 备用选择器1
        'input[name="Password"]',                               # 备用选择器2
        'input[placeholder*="password"]',                       # 备用选择器3
        'input[id*="password"]'                                 # 备用选择器4
    ]

    for selector in selectors:
        try:
            if human_type(page, selector, password):
                print(f"成功填写密码: {selector}")
                return True
        except:
            continue

    print("所有密码输入框选择器都失败了")
    return False


def smart_click_dropdown(page, selector, element_name, config):
    """智能点击下拉框，处理被拦截的情况"""
    click_methods = [
        # 方法1: 强制点击 (优先使用，避免label拦截)
        lambda: page.click(selector, force=True),
        # 方法2: 使用locator强制点击
        lambda: page.locator(selector).click(force=True),
        # 方法3: 点击按钮中心
        lambda: page.click(selector, position={"x": 50, "y": 15}),
        # 方法4: 使用JavaScript点击
        lambda: page.evaluate(f'document.querySelector("{selector}").click()'),
        # 方法5: 普通点击 (最后尝试)
        lambda: page.click(selector),
        # 方法6: 使用locator点击
        lambda: page.locator(selector).click()
    ]

    for i, method in enumerate(click_methods, 1):
        try:
            debug_log(f"尝试点击{element_name} - 方法{i}", config)
            method()
            debug_log(f"成功点击{element_name} - 方法{i}", config)
            return True
        except Exception as e:
            debug_log(f"点击{element_name}方法{i}失败: {str(e)}", config)
            continue

    debug_log(f"所有点击{element_name}的方法都失败了", config)
    return False


def diagnose_page_state(page, config):
    """诊断当前页面状态"""
    try:
        debug_log("诊断页面状态...", config)
        debug_log(f"当前URL: {page.url}", config)

        # 检查页面标题
        try:
            title = page.title()
            debug_log(f"页面标题: {title}", config)
        except:
            debug_log("无法获取页面标题", config)

        # 检查可见的表单元素
        form_elements = [
            "input", "button", "select", "form",
            "[data-testid]", "[role='button']", "[role='combobox']"
        ]

        for element in form_elements:
            try:
                count = page.locator(element).count()
                if count > 0:
                    debug_log(f"找到 {count} 个 {element} 元素", config)
            except:
                continue

        # 检查特定的生日相关元素
        birthdate_elements = [
            "#BirthMonthDropdown", "#BirthDayDropdown",
            "input[name='BirthYear']", "[data-testid='birthdateControls']",
            "button[name='BirthMonth']", "button[name='BirthDay']"
        ]

        for element in birthdate_elements:
            try:
                if page.locator(element).count() > 0:
                    is_visible = page.locator(element).is_visible()
                    debug_log(f"生日元素 {element}: 存在={True}, 可见={is_visible}", config)
            except:
                debug_log(f"生日元素 {element}: 存在=False", config)

    except Exception as e:
        debug_log(f"页面诊断失败: {str(e)}", config)


def wait_for_birthdate_page(page, config, timeout=30000):
    """等待生日页面加载完成"""
    try:
        debug_log("等待生日页面加载...", config)

        # 等待页面稳定
        time.sleep(3)

        # 先诊断页面状态
        diagnose_page_state(page, config)

        # 检查多种可能的生日页面元素
        birthdate_indicators = [
            "#BirthMonthDropdown",
            "#BirthDayDropdown",
            "input[name='BirthYear']",
            "[data-testid='birthdateControls']",
            "button[name='BirthMonth']",
            "button[name='BirthDay']"
        ]

        # 尝试等待任意一个生日元素出现
        for indicator in birthdate_indicators:
            try:
                page.wait_for_selector(indicator, timeout=10000)
                debug_log(f"找到生日页面元素: {indicator}", config)
                return True
            except:
                continue

        debug_log("未找到生日页面元素，检查页面内容", config)

        # 检查页面内容
        try:
            page_content = page.content()
            if "birth" in page_content.lower() or "birthday" in page_content.lower() or "month" in page_content.lower():
                debug_log("页面包含生日相关内容，继续尝试", config)
                return True
        except:
            debug_log("无法获取页面内容", config)

        return False

    except Exception as e:
        debug_log(f"等待生日页面失败: {str(e)}", config)
        return False


def smart_select_birthdate(page, birth_month, birth_day, birth_year):
    """智能选择生日，模仿用户提供的示例代码"""
    try:
        config = load_config()
        debug_log("开始填写生日信息", config)

        # 等待生日页面加载
        if not wait_for_birthdate_page(page, config):
            debug_log("生日页面未正确加载", config)
            debug_screenshot(page, "birthdate_page_not_loaded", config)
            return False

        # 月份名称映射
        month_names = {
            "1": "January", "2": "February", "3": "March", "4": "April",
            "5": "May", "6": "June", "7": "July", "8": "August",
            "9": "September", "10": "October", "11": "November", "12": "December"
        }

        month_name = month_names.get(birth_month, "January")

        # 1. 智能查找并点击月份下拉框
        month_selectors = [
            "#BirthMonthDropdown",
            "button[name='BirthMonth']",
            "[data-testid='birthdateControls'] button:first-child",
            "select[name='BirthMonth']"
        ]

        month_clicked = False
        for selector in month_selectors:
            try:
                if page.locator(selector).is_visible():
                    debug_log(f"找到月份选择器: {selector}", config)
                    if smart_click_dropdown(page, selector, "月份下拉框", config):
                        month_clicked = True
                        break
            except:
                continue

        if not month_clicked:
            debug_log("所有月份选择器都不可用", config)
            debug_screenshot(page, "month_selector_not_found", config)
            return False

        time.sleep(random.uniform(0.5, 1.0))

        # 尝试选择月份选项
        try:
            debug_log(f"选择月份: {month_name}", config)
            page.get_by_role("option", name=month_name).click()
            debug_log(f"成功选择月份: {month_name}", config)
            human_pause(1, 2)  # 月份选择后停留
        except Exception as e:
            debug_log(f"月份选项选择失败: {str(e)}", config)
            # 尝试备用选择方法
            try:
                page.locator(f'[role="option"]:has-text("{month_name}")').first.click()
                debug_log(f"备用方法选择月份成功: {month_name}", config)
                human_pause(1, 2)  # 月份选择后停留
            except:
                debug_log("月份选择完全失败", config)
                return False

        # 2. 智能查找并点击日期下拉框
        day_selectors = [
            "#BirthDayDropdown",
            "button[name='BirthDay']",
            "[data-testid='birthdateControls'] button:nth-child(2)",
            "select[name='BirthDay']"
        ]

        day_clicked = False
        for selector in day_selectors:
            try:
                if page.locator(selector).is_visible():
                    debug_log(f"找到日期选择器: {selector}", config)
                    if smart_click_dropdown(page, selector, "日期下拉框", config):
                        day_clicked = True
                        break
            except:
                continue

        if not day_clicked:
            debug_log("所有日期选择器都不可用", config)
            debug_screenshot(page, "day_selector_not_found", config)
            return False

        time.sleep(random.uniform(0.5, 1.0))

        # 尝试选择日期选项
        try:
            debug_log(f"选择日期: {birth_day}", config)
            page.get_by_role("option", name=birth_day).click()
            debug_log(f"成功选择日期: {birth_day}", config)
            human_pause(1, 2)  # 日期选择后停留
        except Exception as e:
            debug_log(f"日期选项选择失败: {str(e)}", config)
            # 尝试备用选择方法
            try:
                page.locator(f'[role="option"]:has-text("{birth_day}")').first.click()
                debug_log(f"备用方法选择日期成功: {birth_day}", config)
                human_pause(1, 2)  # 日期选择后停留
            except:
                debug_log("日期选择完全失败", config)
                return False

        # 3. 智能查找并填写出生年份
        year_selectors = [
            "input[name='BirthYear']",
            "input[type='number'][name='BirthYear']",
            "[data-testid='birthdateControls'] input",
            "select[name='BirthYear']"
        ]

        year_filled = False
        for selector in year_selectors:
            try:
                if page.locator(selector).is_visible():
                    debug_log(f"找到年份输入框: {selector}", config)

                    if 'input' in selector:
                        # 输入框类型 - 优先使用人工逐字符输入
                        try:
                            # 先尝试人工逐字符输入，更有输入感觉
                            if human_type(page, selector, birth_year):
                                debug_log(f"人工逐字符输入年份成功: {birth_year}", config)
                                human_pause(1, 2)  # 年份填写后停留
                                year_filled = True
                                break
                            else:
                                # 备用：快速填写
                                page.fill(selector, birth_year)
                                debug_log(f"快速填写年份成功: {birth_year}", config)
                                human_pause(1, 2)  # 年份填写后停留
                                year_filled = True
                                break
                        except Exception as e:
                            debug_log(f"年份输入失败: {str(e)}", config)
                            continue
                    else:
                        # 选择框类型
                        if human_select(page, selector, birth_year):
                            debug_log(f"选择年份成功: {birth_year}", config)
                            human_pause(1, 2)  # 年份选择后停留
                            year_filled = True
                            break
            except:
                continue

        if not year_filled:
            debug_log("所有年份输入方法都失败了", config)
            debug_screenshot(page, "year_input_failed", config)
            return False

        time.sleep(random.uniform(0.5, 1.0))

        debug_log("生日信息填写完成", config)
        return True

    except Exception as e:
        debug_log(f"生日选择过程出错: {str(e)}", config)
        return False


def human_select(page, locator, value, min_delay=0.3, max_delay=1.0, timeout=None):
    """模拟人工选择下拉框"""
    if timeout is None:
        config = load_config()
        timeout = config.get('timeouts', {}).get('element_wait', 10000)

    try:
        element = page.locator(locator)
        element.wait_for(state='visible', timeout=timeout)

        # 选择前的随机停顿
        time.sleep(random.uniform(0.2, 0.6))
        element.select_option(value)
        # 选择后的随机停顿
        time.sleep(random.uniform(min_delay, max_delay))
        return True
    except:
        return False


def human_pause(min_seconds=1.0, max_seconds=3.0):
    """人性化停留时间"""
    pause_time = random.uniform(min_seconds, max_seconds)
    print(f"停留 {pause_time:.1f} 秒...")
    time.sleep(pause_time)


def human_behavior(page):
    """模拟人类随机行为"""
    behaviors = [
        lambda: page.mouse.move(random.randint(100, 800), random.randint(100, 600)),
        lambda: page.evaluate("window.scrollBy(0, {})".format(random.randint(-100, 100))),
        lambda: time.sleep(random.uniform(0.5, 2.0))
    ]

    # 随机执行1-2个行为
    for _ in range(random.randint(1, 2)):
        behavior = random.choice(behaviors)
        try:
            behavior()
        except:
            pass  # 忽略可能的错误
        time.sleep(random.uniform(0.2, 0.8))


def create_temp_email():
    """创建临时邮箱"""
    base_url = "https://api.mail.tm"

    # 生成随机邮箱和密码
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

    # 获取域名
    r = requests.get(f"{base_url}/domains")
    domain = r.json()["hydra:member"][0]["domain"]
    email = f"{username}@{domain}"

    # 注册邮箱
    payload = {"address": email, "password": password}
    requests.post(f"{base_url}/accounts", json=payload)

    # 登录获取token
    r = requests.post(f"{base_url}/token", json=payload)
    token = r.json()["token"]

    print(f"创建临时邮箱: {email}")
    return {'email': email, 'password': password, 'token': token}


def wait_for_page_ready(page, timeout=30000):
    """等待页面完全准备好"""
    try:
        # 等待页面基本加载
        page.wait_for_load_state('domcontentloaded', timeout=timeout)

        # 等待一些常见元素出现
        common_selectors = [
            'input[name="Email"]',
            'input[type="email"]',
            'form',
            'button',
            'input'
        ]

        for selector in common_selectors:
            try:
                page.wait_for_selector(selector, timeout=5000)
                print(f'找到元素: {selector}')
                return True
            except:
                continue

        print('使用基本等待策略')
        time.sleep(3)
        return True

    except Exception as e:
        print(f'等待页面准备失败: {str(e)}')
        return False


def safe_page_goto(page, url, max_retries=3):
    """安全的页面跳转，带重试机制"""
    config = load_config()
    timeouts = config.get('timeouts', {})

    for attempt in range(max_retries):
        try:
            print(f'尝试打开页面 (第{attempt + 1}次)...')
            page.goto(url, timeout=timeouts.get('page_load', 90000))

            # 等待页面准备好
            if wait_for_page_ready(page, timeouts.get('navigation', 90000)):
                print('页面加载完成')
                return True
            else:
                print('页面未完全准备好，但继续尝试')
                return True

        except Exception as e:
            print(f'页面加载失败 (第{attempt + 1}次): {str(e)}')
            if attempt < max_retries - 1:
                print(f'等待5秒后重试...')
                time.sleep(5)
            else:
                print('所有重试都失败了')
                return False

    return False


def debug_screenshot(page, filename, config):
    """调试截图"""
    try:
        if config.get('debug', {}).get('screenshot_on_error', False):
            page.screenshot(path=f"debug_{filename}_{int(time.time())}.png")
            print(f"已保存调试截图: debug_{filename}_{int(time.time())}.png")
    except:
        pass


def debug_log(message, config):
    """调试日志"""
    if config.get('debug', {}).get('verbose_logging', False):
        print(f"[DEBUG] {message}")


def handle_human_verification(page, config):
    """处理人机验证"""
    try:
        debug_log("检测到人机验证页面", config)

        # 检查是否存在验证元素
        verification_indicators = [
            "Let's prove you're human",
            "Press and hold the button",
            "humanCaptchaIframe",
            "Verification challenge"
        ]

        page_content = page.content()
        is_verification_page = any(indicator in page_content for indicator in verification_indicators)

        if not is_verification_page:
            debug_log("未检测到人机验证页面", config)
            return True

        debug_log("确认为人机验证页面，开始处理", config)
        debug_screenshot(page, "human_verification", config)

        print("=" * 50)
        print("检测到人机验证！")
        print("页面提示: Press and hold the button")
        print("=" * 50)

        # 等待iframe加载
        try:
            iframe_selector = 'iframe[data-testid="humanCaptchaIframe"]'
            page.wait_for_selector(iframe_selector, timeout=10000)
            debug_log("验证iframe已加载", config)
        except:
            debug_log("验证iframe加载超时", config)

        # 给用户一些时间准备
        print("准备使用pyautogui处理验证...")
        print("等待验证按钮完全加载...")

        # 随机等待2-5秒，确保验证按钮完全加载
        wait_time = random.uniform(2, 5)
        print(f"等待 {wait_time:.1f} 秒确保按钮加载完成...")
        time.sleep(wait_time)

        print("请确保浏览器窗口可见，开始验证...")
        time.sleep(1)

        # 使用pyautogui处理验证
        success = handle_captcha_with_pyautogui(page, config)

        if success:
            debug_log("人机验证处理成功", config)
            # 等待页面跳转
            time.sleep(5)
            return True
        else:
            debug_log("人机验证处理失败", config)
            return False

    except Exception as e:
        debug_log(f"人机验证处理异常: {str(e)}", config)
        return False


def handle_passkey_page(page, config):
    """处理Passkey页面，点击Skip for now"""
    try:
        debug_log("开始处理Passkey页面", config)

        # 等待页面加载完成
        time.sleep(2)

        # 查找并点击"Skip for now"按钮
        skip_selectors = [
            'button[data-testid="secondaryButton"]',  # 主要选择器 ⭐
            'button:has-text("Skip for now")',        # 文本选择器
            'button[type="button"]:has-text("Skip for now")',  # 类型+文本选择器
            '.fui-Button:has-text("Skip for now")',   # class + 文本选择器
            'button:contains("Skip")'                 # 包含Skip的按钮 (最后尝试)
        ]

        for selector in skip_selectors:
            try:
                debug_log(f"尝试Skip按钮选择器: {selector}", config)
                locator = page.locator(selector)
                if locator.is_visible():
                    debug_log(f"找到Skip按钮: {selector}", config)
                    page.click(selector)
                    debug_log("成功点击Skip for now按钮", config)
                    human_pause(1, 2)  # 点击后停留
                    return True
                else:
                    debug_log(f"Skip按钮选择器 {selector} 不可见", config)
            except Exception as e:
                debug_log(f"Skip按钮选择器 {selector} 失败: {str(e)}", config)
                continue

        debug_log("未找到Skip for now按钮", config)
        return False

    except Exception as e:
        debug_log(f"处理Passkey页面异常: {str(e)}", config)
        return False


def handle_stay_signed_in_page(page, config):
    """处理Stay signed in页面，点击Yes"""
    try:
        debug_log("开始处理Stay signed in页面", config)

        # 等待页面加载完成
        time.sleep(2)

        # 查找并点击"Yes"按钮
        yes_selectors = [
            'button[data-testid="primaryButton"]',    # 主要选择器
            'button:has-text("Yes")',                 # 文本选择器
            'button:contains("Yes")',                 # 包含Yes的按钮
            '.fui-Button:has-text("Yes")'             # class + 文本选择器
        ]

        for selector in yes_selectors:
            try:
                if page.locator(selector).is_visible():
                    debug_log(f"找到Yes按钮: {selector}", config)
                    page.click(selector)
                    debug_log("成功点击Yes按钮", config)
                    human_pause(1, 2)  # 点击后停留
                    return True
            except Exception as e:
                debug_log(f"Yes按钮选择器 {selector} 失败: {str(e)}", config)
                continue

        debug_log("未找到Yes按钮", config)
        return False

    except Exception as e:
        debug_log(f"处理Stay signed in页面异常: {str(e)}", config)
        return False


def handle_privacy_notice_page(page, config):
    """处理隐私声明页面，点击OK"""
    try:
        debug_log("开始处理隐私声明页面", config)

        # 等待页面加载完成
        time.sleep(2)

        # 查找并点击"OK"按钮
        ok_selectors = [
            'button:has-text("OK")',                  # 文本选择器
            'button.ms-Button--primary',              # 主要按钮class
            'button[data-is-focusable="true"]',       # 可聚焦按钮
            '.ms-Button-label:has-text("OK")',        # label文本选择器
            'button:contains("OK")'                   # 包含OK的按钮
        ]

        for selector in ok_selectors:
            try:
                if page.locator(selector).is_visible():
                    debug_log(f"找到OK按钮: {selector}", config)
                    page.click(selector)
                    debug_log("成功点击OK按钮", config)
                    human_pause(1, 2)  # 点击后停留

                    # 点击后额外等待，确保页面开始跳转
                    debug_log("等待页面开始跳转...", config)
                    time.sleep(2)
                    return True
            except Exception as e:
                debug_log(f"OK按钮选择器 {selector} 失败: {str(e)}", config)
                continue

        debug_log("未找到OK按钮", config)
        return False

    except Exception as e:
        debug_log(f"处理隐私声明页面异常: {str(e)}", config)
        return False


def wait_for_verification_result(page, config, max_wait_seconds=60):
    """智能等待验证结果和页面跳转"""
    try:
        debug_log("开始等待验证结果...", config)
        print("等待验证结果和页面跳转...")

        start_time = time.time()
        check_interval = 1  # 每1秒检查一次，提高检测频率
        last_url = ""  # 记录上一次的URL，用于检测重定向
        redirect_count = 0  # 记录重定向次数

        while time.time() - start_time < max_wait_seconds:
            try:
                # 确保获取最新的URL - 等待页面稳定
                try:
                    page.wait_for_load_state("domcontentloaded", timeout=1000)
                except:
                    pass

                # 获取当前URL
                current_url = page.url

                # 如果URL没有变化，尝试强制刷新获取真实URL
                if current_url == last_url and last_url != "":
                    try:
                        # 尝试等待网络空闲状态
                        page.wait_for_load_state("networkidle", timeout=2000)
                        # 重新获取URL
                        new_url = page.url
                        if new_url != current_url:
                            debug_log(f"强制刷新后发现URL变化: {current_url} → {new_url}", config)
                            current_url = new_url
                    except:
                        pass

                # 检测URL变化（重定向）
                if current_url != last_url and last_url != "":
                    redirect_count += 1
                    debug_log(f"检测到重定向 #{redirect_count}: {last_url} → {current_url}", config)
                    print(f"页面重定向 #{redirect_count}: {current_url}")

                last_url = current_url
                debug_log(f"当前页面URL: {current_url}", config)

                # 检查是否跳转到隐私声明页面
                if (current_url.startswith("https://privacynotice.account.microsoft.com/notice?")):
                    elapsed_time = time.time() - start_time
                    print(f"✓ 验证成功！跳转到隐私声明页面 (耗时 {elapsed_time:.1f} 秒)")
                    debug_log(f"检测到隐私声明页面: {current_url}", config)

                    # 处理隐私声明页面，点击"OK"
                    if handle_privacy_notice_page(page, config):
                        print("✓ 已确认隐私声明")
                        # 等待页面跳转并重新检测URL - 增加等待时间
                        debug_log("等待隐私声明页面跳转...", config)
                        time.sleep(3)  # 增加到3秒

                        # 多次尝试检测URL变化
                        for attempt in range(3):
                            try:
                                page.wait_for_load_state("networkidle", timeout=5000)
                                new_url = page.url
                                debug_log(f"隐私声明处理后的新URL (尝试{attempt+1}): {new_url}", config)

                                if new_url != current_url:
                                    print(f"页面已跳转到: {new_url}")
                                    last_url = new_url
                                    break
                                else:
                                    debug_log(f"URL未变化，等待1秒后重试...", config)
                                    time.sleep(1)
                            except Exception as e:
                                debug_log(f"检测URL变化失败 (尝试{attempt+1}): {str(e)}", config)
                                time.sleep(1)

                        # 强制跳出隐私声明检测，避免重复处理
                        debug_log("隐私声明页面处理完成，强制进入下一轮检测", config)

                        # 强制刷新URL获取，确保获取到真实的当前页面
                        try:
                            debug_log("强制刷新页面状态以获取真实URL", config)
                            page.wait_for_load_state("networkidle", timeout=3000)
                            time.sleep(2)  # 额外等待
                            fresh_url = page.url
                            debug_log(f"强制刷新后的真实URL: {fresh_url}", config)

                            # 如果URL确实已经变化，更新last_url
                            if fresh_url != current_url:
                                debug_log(f"强制刷新发现URL已变化: {current_url} → {fresh_url}", config)
                                last_url = fresh_url
                                print(f"强制检测到页面已跳转: {fresh_url}")
                        except Exception as e:
                            debug_log(f"强制刷新URL失败: {str(e)}", config)

                        continue
                    else:
                        print("⚠️ 隐私声明页面处理失败")
                        return True

                # 检查是否跳转到Passkey页面
                if (current_url.startswith("https://account.live.com/interrupt/passkey?")):
                    elapsed_time = time.time() - start_time
                    print(f"✓ 验证成功！跳转到Passkey页面 (耗时 {elapsed_time:.1f} 秒)")
                    debug_log(f"检测到Passkey页面: {current_url}", config)

                    # 处理Passkey页面，点击"Skip for now"
                    if handle_passkey_page(page, config):
                        print("✓ 已跳过Passkey设置")
                        # 等待页面跳转并重新检测URL
                        time.sleep(2)
                        try:
                            page.wait_for_load_state("networkidle", timeout=3000)
                            new_url = page.url
                            debug_log(f"Passkey处理后的新URL: {new_url}", config)
                            if new_url != current_url:
                                print(f"页面已跳转到: {new_url}")
                                last_url = new_url
                        except:
                            pass
                        # 继续等待下一个页面
                        continue
                    else:
                        print("⚠️ Passkey页面处理失败")
                        return True

                # 检查是否跳转到Stay signed in页面
                if (current_url.startswith("https://login.live.com/login.srf")):
                    elapsed_time = time.time() - start_time
                    print(f"✓ 跳转到Stay signed in页面 (耗时 {elapsed_time:.1f} 秒)")
                    debug_log(f"检测到Stay signed in页面: {current_url}", config)

                    # 处理Stay signed in页面，点击"Yes"
                    if handle_stay_signed_in_page(page, config):
                        print("✓ 已选择保持登录")
                        # 等待页面跳转并重新检测URL
                        time.sleep(2)
                        try:
                            page.wait_for_load_state("networkidle", timeout=3000)
                            new_url = page.url
                            debug_log(f"Stay signed in处理后的新URL: {new_url}", config)
                            if new_url != current_url:
                                print(f"页面已跳转到: {new_url}")
                                last_url = new_url
                        except:
                            pass
                        # 继续等待最终页面
                        continue
                    else:
                        print("⚠️ Stay signed in页面处理失败")
                        return True

                # 检查是否跳转到最终成功页面
                if ("account.microsoft.com" in current_url and
                    "interrupt" not in current_url and
                    "ppsecure" not in current_url and
                    "login.live.com" not in current_url):
                    elapsed_time = time.time() - start_time
                    print(f"🎉 注册完全成功！已跳转到Microsoft账户页面 (总耗时 {elapsed_time:.1f} 秒)")
                    debug_log(f"注册完全成功，最终页面: {current_url}", config)
                    return True

                # 检查是否是中间重定向页面（需要继续等待）
                if ("login.live.com/ppsecure" in current_url or
                    "post.srf" in current_url):
                    elapsed_time = time.time() - start_time
                    debug_log(f"检测到中间重定向页面，继续等待: {current_url}", config)
                    print(f"检测到中间重定向，继续等待... ({elapsed_time:.0f}s)")

                    # 每次都尝试强制获取最新URL，不只是30秒后
                    try:
                        # 等待页面稳定并获取最新URL
                        page.wait_for_load_state("networkidle", timeout=1000)
                        fresh_url = page.url
                        debug_log(f"强制获取的最新URL: {fresh_url}", config)

                        # 如果URL已经变化，说明页面已经跳转
                        if fresh_url != current_url:
                            debug_log(f"发现URL已变化: {current_url} → {fresh_url}", config)
                            print(f"检测到页面已跳转: {fresh_url}")
                            # 更新URL并重新进入检测流程
                            current_url = fresh_url
                            last_url = fresh_url
                            # 不continue，让它进入正常的页面检测流程
                        else:
                            # URL确实没有变化，继续等待
                            if elapsed_time > 30:  # 30秒后提示可能有问题
                                debug_log("中间重定向页面停留时间过长，可能需要手动检查", config)
                                print(f"⚠️ 在中间重定向页面停留过久 ({elapsed_time:.0f}s)，继续等待...")
                            time.sleep(check_interval)
                            continue
                    except:
                        # 获取URL失败，继续等待
                        time.sleep(check_interval)
                        continue

                # 检查是否跳转到其他成功页面（排除中间重定向）
                success_indicators = [
                    "outlook.live.com"
                ]

                for indicator in success_indicators:
                    if (indicator in current_url and
                        "signup.live.com/?lic=1" not in current_url and
                        "ppsecure" not in current_url and
                        "post.srf" not in current_url):
                        elapsed_time = time.time() - start_time
                        print(f"✓ 验证成功！页面已跳转 (耗时 {elapsed_time:.1f} 秒)")
                        debug_log(f"验证成功，跳转到: {current_url}", config)
                        return True

                # 检查页面内容是否还包含验证相关内容
                try:
                    page_content = page.content()
                    if ("prove you're human" not in page_content.lower() and
                        "press and hold" not in page_content.lower() and
                        "verification challenge" not in page_content.lower()):

                        # 再等待一下看是否会跳转
                        time.sleep(3)
                        new_url = page.url
                        if new_url != current_url:
                            elapsed_time = time.time() - start_time
                            print(f"✓ 验证成功！页面内容已更新并跳转 (耗时 {elapsed_time:.1f} 秒)")
                            debug_log(f"验证成功，最终URL: {new_url}", config)
                            return True
                except Exception as e:
                    debug_log(f"检查页面内容失败: {str(e)}", config)

                # 显示等待进度
                elapsed_time = time.time() - start_time
                print(f"等待验证结果... ({elapsed_time:.0f}s/{max_wait_seconds}s)")

                time.sleep(check_interval)

            except Exception as e:
                debug_log(f"检查验证状态时出错: {str(e)}", config)
                time.sleep(check_interval)

        # 超时处理
        print(f"⚠️ 等待验证结果超时 ({max_wait_seconds}秒)")
        debug_log("验证结果等待超时", config)

        # 最后检查一次当前状态
        try:
            final_url = page.url
            debug_log(f"超时时的最终URL: {final_url}", config)

            if final_url != "https://signup.live.com/?lic=1":
                print("? 页面已发生变化，可能验证已完成")
                return True
            else:
                print("? 验证状态不确定，请手动检查")
                input("请手动完成验证后按回车继续...")
                return True
        except:
            print("? 无法确定验证状态")
            input("请手动完成验证后按回车继续...")
            return True

    except Exception as e:
        debug_log(f"等待验证结果异常: {str(e)}", config)
        print("? 验证状态检查出错，请手动确认")
        input("请手动完成验证后按回车继续...")
        return True


def find_captcha_button_position(page, config):
    """查找验证按钮的实际位置"""
    try:
        debug_log("开始查找验证按钮位置", config)

        # 方法1: 尝试获取iframe的位置和大小
        iframe_selector = 'iframe[data-testid="humanCaptchaIframe"]'
        iframe = page.locator(iframe_selector)

        if iframe.count() > 0:
            # 获取iframe的边界框
            bbox = iframe.bounding_box()
            if bbox:
                # 计算iframe中心位置，稍微向下偏移到按钮区域，再额外向下35px
                iframe_center_x = bbox['x'] + bbox['width'] / 2
                iframe_center_y = bbox['y'] + bbox['height'] * 0.7 + 40  # 向下偏移到按钮区域 + 40px
                debug_log(f"找到iframe位置: x={bbox['x']}, y={bbox['y']}, width={bbox['width']}, height={bbox['height']}", config)
                debug_log(f"计算按钮位置: ({iframe_center_x}, {iframe_center_y}) [+35px向下偏移]", config)
                return int(iframe_center_x), int(iframe_center_y)

        # 方法2: 查找验证描述元素，按钮通常在其下方
        description_selector = '[data-testid="humanCaptchaDescription"]'
        description = page.locator(description_selector)

        if description.count() > 0:
            bbox = description.bounding_box()
            if bbox:
                # 按钮通常在描述下方100-150像素处，再额外向下35px
                button_x = bbox['x'] + bbox['width'] / 2
                button_y = bbox['y'] + bbox['height'] + 120 + 40  # 向下偏移到按钮位置 + 40px
                debug_log(f"通过描述元素找到按钮位置: ({button_x}, {button_y}) [+35px向下偏移]", config)
                return int(button_x), int(button_y)

        # 方法3: 查找验证容器元素
        container_selectors = [
            '.___183jwsf',  # 验证容器的class
            'iframe[title="Verification challenge"]',
            '[class*="captcha"]'
        ]

        for selector in container_selectors:
            try:
                element = page.locator(selector)
                if element.count() > 0:
                    bbox = element.bounding_box()
                    if bbox:
                        center_x = bbox['x'] + bbox['width'] / 2
                        center_y = bbox['y'] + bbox['height'] / 2 + 40  # 额外向下 40px
                        debug_log(f"通过容器 {selector} 找到验证区域: ({center_x}, {center_y}) [+35px向下偏移]", config)
                        return int(center_x), int(center_y)
            except:
                continue

        # 方法4: 使用页面中心区域（稍微向下偏移）
        try:
            viewport = page.viewport_size()
            if viewport:
                center_x = viewport['width'] / 2
                center_y = viewport['height'] / 2 + 50 + 40  # 稍微向下偏移 + 40px
                debug_log(f"使用页面中心位置: ({center_x}, {center_y}) [+35px向下偏移]", config)
                return int(center_x), int(center_y)
        except:
            pass

        debug_log("无法找到验证按钮位置", config)
        return None, None

    except Exception as e:
        debug_log(f"查找验证按钮位置失败: {str(e)}", config)
        return None, None


def handle_captcha_with_pyautogui(page, config):
    """使用pyautogui处理验证码"""
    try:
        debug_log("开始使用pyautogui处理验证", config)

        # 设置pyautogui安全设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3

        print("正在寻找验证按钮...")

        # 查找验证按钮的实际位置
        button_x, button_y = find_captcha_button_position(page, config)

        if button_x is None or button_y is None:
            # 使用屏幕中心作为备用
            screen_width, screen_height = pyautogui.size()
            button_x = screen_width // 2
            button_y = screen_height // 2
            debug_log(f"使用屏幕中心位置: ({button_x}, {button_y})", config)

        debug_log(f"目标点击位置: ({button_x}, {button_y})", config)

        # 移动到目标位置
        pyautogui.moveTo(button_x, button_y, duration=1)
        time.sleep(0.5)

        # 按住按钮（长按）
        print("开始长按验证按钮...")
        pyautogui.mouseDown()

        # 保持按住状态，通常需要3-5秒
        hold_duration = random.uniform(10, 15)
        debug_log(f"按住按钮 {hold_duration:.1f} 秒", config)

        for i in range(int(hold_duration)):
            print(f"按住中... {i+1}/{int(hold_duration)}")
            time.sleep(1)

        # 释放按钮
        pyautogui.mouseUp()
        print("释放按钮")

        debug_log("pyautogui验证操作完成", config)

        # 智能等待验证结果和页面跳转
        return wait_for_verification_result(page, config)

    except Exception as e:
        debug_log(f"pyautogui操作失败: {str(e)}", config)
        print(f"自动验证失败: {str(e)}")
        print("请手动完成验证...")

        # 给用户手动操作的时间
        input("请手动完成验证后按回车继续...")
        return True


def register_outlook_account(ws_url):
    """注册Outlook账号"""
    try:
        # 加载配置
        config = load_config()
        timeouts = config.get('timeouts', {})

        debug_log("开始注册流程", config)

        playwright = sync_playwright().start()
        browser = playwright.chromium.connect_over_cdp(ws_url)
        page = browser.contexts[0].new_page()

        # 设置页面超时
        page.set_default_timeout(timeouts.get('element_wait', 90000))
        page.set_default_navigation_timeout(timeouts.get('navigation', 90000))

        debug_log(f"设置超时: element_wait={timeouts.get('element_wait', 90000)}, navigation={timeouts.get('navigation', 90000)}", config)

        print('打开Outlook注册页面...')
        if not safe_page_goto(page, 'https://signup.live.com/'):
            debug_screenshot(page, "page_load_failed", config)
            return {'success': False, 'error': '页面加载失败'}

        # 页面加载后的随机等待和行为
        time.sleep(random.uniform(2, 4))
        human_behavior(page)

        # 生成随机用户名和密码
        # 确保用户名以字母开头，符合Outlook要求
        username_patterns = [
            # 模式1: 字母开头 + 随机字母数字 (8-12位)
            lambda: random.choice(string.ascii_lowercase) + ''.join(random.choices(string.ascii_lowercase + string.digits, k=random.randint(7, 11))),
            # 模式2: 字母开头 + 纯字母 + 数字结尾 (8-12位)
            lambda: random.choice(string.ascii_lowercase) + ''.join(random.choices(string.ascii_lowercase, k=random.randint(4, 7))) + ''.join(random.choices(string.digits, k=random.randint(2, 4))),
            # 模式3: 两个字母开头 + 数字 + 字母 (8-12位)
            lambda: ''.join(random.choices(string.ascii_lowercase, k=2)) + ''.join(random.choices(string.digits, k=random.randint(2, 4))) + ''.join(random.choices(string.ascii_lowercase, k=random.randint(4, 6)))
        ]

        username = random.choice(username_patterns)()

        # 确保用户名长度在8-15位之间（Outlook要求）
        if len(username) < 8:
            username += ''.join(random.choices(string.ascii_lowercase + string.digits, k=8-len(username)))
        elif len(username) > 15:
            username = username[:15]


        username = username + "@outlook.com"
        password = ''.join(random.choices(string.ascii_letters + string.digits + '!@#$%', k=16))

        print(f"注册用户名: {username}")
        print(f"注册密码: {password}")
        debug_log(f"生成用户名: {username}, 密码长度: {len(password)}", config)

        # 人性化填写邮箱地址
        print("填写邮箱地址...")
        debug_log("开始填写邮箱地址", config)

        # 检查页面状态
        debug_log(f"当前页面URL: {page.url}", config)
        debug_screenshot(page, "before_email_input", config)

        human_behavior(page)  # 填写前的随机行为
        if human_type(page, 'input[name="Email"]', username):
            debug_log("邮箱地址填写成功", config)
            human_pause(2, 4)  # 邮箱填写完成后停留
            human_behavior(page)  # 填写后的随机行为
            # 使用智能点击函数
            if smart_click_next(page, 2, 4):
                debug_log("Next按钮点击成功", config)
                human_pause(1, 2)  # 点击后停留
            else:
                debug_log("Next按钮点击失败", config)
                debug_screenshot(page, "next_button_failed", config)
        else:
            debug_log("邮箱地址填写失败", config)
            debug_screenshot(page, "email_input_failed", config)
            return {'success': False, 'error': '邮箱地址填写失败'}

        # 人性化填写密码
        print("填写密码...")
        human_behavior(page)
        # 使用智能密码输入函数
        if smart_type_password(page, password):
            human_pause(2, 4)  # 密码填写完成后停留
            human_behavior(page)
            # 使用智能点击函数
            smart_click_next(page, 2, 4)
            human_pause(1, 2)  # 点击后停留


        # 人性化填写生日
        print("填写生日信息...")
        debug_log("开始填写生日信息", config)
        debug_screenshot(page, "before_birthdate", config)

        # 随机生成生日
        birth_month = str(random.randint(1, 12))
        birth_day = str(random.randint(1, 28))
        birth_year = str(random.randint(1980, 2000))

        debug_log(f"生成生日: {birth_month}/{birth_day}/{birth_year}", config)

        human_behavior(page)

        # 使用智能生日选择函数
        if smart_select_birthdate(page, birth_month, birth_day, birth_year):
            debug_log("生日信息填写成功", config)
            human_pause(2, 4)  # 生日填写完成后停留
            human_behavior(page)
            # 提交表单
            if smart_click_next(page, 3, 6):
                debug_log("生日页面提交成功", config)
                human_pause(1, 2)  # 点击后停留
            else:
                debug_log("生日页面提交失败", config)
                debug_screenshot(page, "birthdate_submit_failed", config)
        else:
            debug_log("生日信息填写失败", config)
            debug_screenshot(page, "birthdate_failed", config)
            return {'success': False, 'error': '生日信息填写失败'}


        # 人性化填写姓名
        first_name = ''.join(random.choices(string.ascii_uppercase, k=1)) + ''.join(random.choices(string.ascii_lowercase, k=5))
        last_name = ''.join(random.choices(string.ascii_uppercase, k=1)) + ''.join(random.choices(string.ascii_lowercase, k=6))

        print("填写姓名...")
        debug_log("开始填写姓名信息", config)
        debug_log(f"生成姓名: {first_name} {last_name}", config)
        debug_screenshot(page, "before_name_input", config)

        human_behavior(page)

        # 使用新的输入框ID
        first_name_success = False
        last_name_success = False

        # 尝试填写名字
        first_name_selectors = [
            '#firstNameInput',              # 新的ID选择器
            'input[name="firstNameInput"]', # name属性选择器
            'input[name="FirstName"]',      # 旧的name属性
            '[data-testid="firstNameInput"] input'  # 通过testid查找
        ]

        for selector in first_name_selectors:
            try:
                if human_type(page, selector, first_name):
                    debug_log(f"成功填写名字: {selector}", config)
                    first_name_success = True
                    break
            except:
                continue

        if not first_name_success:
            debug_log("名字填写失败", config)
            debug_screenshot(page, "first_name_failed", config)
            return {'success': False, 'error': '名字填写失败'}

        human_behavior(page)

        # 尝试填写姓氏
        last_name_selectors = [
            '#lastNameInput',               # 新的ID选择器
            'input[name="lastNameInput"]',  # name属性选择器
            'input[name="LastName"]',       # 旧的name属性
            '[data-testid="lastNameInput"] input'   # 通过testid查找
        ]

        for selector in last_name_selectors:
            try:
                if human_type(page, selector, last_name):
                    debug_log(f"成功填写姓氏: {selector}", config)
                    last_name_success = True
                    break
            except:
                continue

        if not last_name_success:
            debug_log("姓氏填写失败", config)
            debug_screenshot(page, "last_name_failed", config)
            return {'success': False, 'error': '姓氏填写失败'}

        debug_log("姓名信息填写完成", config)
        human_pause(2, 4)  # 姓名填写完成后停留
        human_behavior(page)

        # 使用智能点击函数
        if smart_click_next(page, 2, 4):
            debug_log("姓名页面提交成功", config)
            human_pause(1, 2)  # 点击后停留
        else:
            debug_log("姓名页面提交失败", config)
            debug_screenshot(page, "name_submit_failed", config)

        # 等待页面加载并检查是否出现人机验证
        time.sleep(3)
        debug_log(f"提交后页面URL: {page.url}", config)

        # 检查是否出现人机验证
        if handle_human_verification(page, config):
            debug_log("人机验证处理完成", config)
        else:
            debug_log("人机验证处理失败", config)
            debug_screenshot(page, "verification_failed", config)

        input("注册成功后按回车继续...")
        # 检查注册结果
        if "outlook.live.com" in page.url or "account.microsoft.com" in page.url:
            result = {
                'success': True,
                'email': username,
                'password': password,
                'first_name': first_name if 'first_name' in locals() else '',
                'last_name': last_name if 'last_name' in locals() else ''
            }
            print("注册成功！")
        else:
            result = {'success': False, 'error': '注册未完成', 'url': page.url}
            print(f"注册失败，当前页面: {page.url}")

        page.close()
        playwright.stop()
        return result

    except Exception as e:
        print(f"注册错误: {str(e)}")
        return {'success': False, 'error': str(e)}


def save_account(account_info, output_file):
    """保存账号信息到文件"""
    if not account_info.get('success', False):
        return False

    try:
        account_str = (
            f"Email: {account_info.get('email', 'N/A')}\n"
            f"Password: {account_info.get('password', 'N/A')}\n"
            f"First Name: {account_info.get('first_name', 'N/A')}\n"
            f"Last Name: {account_info.get('last_name', 'N/A')}\n"
            f"Registration Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"{'-' * 50}\n"
        )

        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(account_str)

        print(f"账号信息已保存")
        return True

    except Exception as e:
        print(f"保存失败: {str(e)}")
        return False


def main():
    """主程序入口"""
    print("=" * 50)
    print("Outlook批量注册工具")
    print("=" * 50)

    # 加载配置
    config = load_config()
    print("配置加载完成")

    register_count = config['register_count']
    print(f"计划注册 {register_count} 个Outlook账号")

    successful_count = 0
    for i in range(1, register_count + 1):
        print(f"\n开始注册第 {i}/{register_count} 个账号")

        try:
            # 生成新的代理账号
            proxy_account = generate_proxy_account(
                config['proxy']['base_account'],
                config['proxy'].get('minute', 0),
                config['proxy'].get('states'),
                config['proxy'].get('cities')
            )
            print(f"生成代理账号: {proxy_account}")
            # 创建浏览器
            browser_id = create_browser(
                config['bitbrowser_url'],
                proxy_account,
                config['proxy']['password'],
                config['proxy']['host'],
                config['proxy']['port']
            )

            if not browser_id:
                print("创建浏览器失败，跳过")
                continue

            # 打开浏览器
            ws = open_browser(config['bitbrowser_url'], browser_id)
            print(f"浏览器已打开")

            # 注册Outlook账号
            account_info = register_outlook_account(ws)

            # 保存账号信息
            if account_info.get('success', False):
                save_account(account_info, config['output_file'])
                successful_count += 1

            # 关闭浏览器
            close_browser(config['bitbrowser_url'], browser_id)

            # 等待
            if i < register_count:
                wait_time = random.randint(30, 60)
                print(f"等待 {wait_time} 秒...")
                time.sleep(wait_time)

        except Exception as e:
            print(f"注册错误: {str(e)}")
            continue

    print(f"\n注册完成! 成功 {successful_count}/{register_count} 个账号")
    print(f"账号信息保存在 {config['output_file']}")
    print("=" * 50)


if __name__ == "__main__":
    main()