# Outlook批量注册工具

简化版的Outlook账号批量注册工具，只保留核心功能。

## 文件结构

```
outlook_register/
├── main.py            # 主程序（包含所有功能）
├── config.json        # 配置文件
├── requirements.txt   # 项目依赖
└── README.md          # 项目说明
```

## 功能特性

- ✅ 批量注册Outlook账号
- ✅ 每次注册自动生成新的代理账号
- ✅ 自动创建和管理比特浏览器
- ✅ 自动保存注册成功的账号信息
- ✅ 人性化输入模拟（逐字符输入、随机延迟）
- ✅ 模拟真实用户行为（鼠标移动、页面滚动）

## 安装

1. 克隆项目到本地
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 安装浏览器：
   ```bash
   python -m playwright install chromium
   ```

### 依赖说明

项目依赖包含在 `requirements.txt` 文件中：

- **playwright>=1.40.0**: 浏览器自动化框架
- **requests>=2.31.0**: HTTP请求库
- **pyautogui>=0.9.54**: GUI自动化库（用于人机验证）
- **pymsgbox>=1.0.9**: 消息框库
- **pytweening>=1.0.4**: 动画缓动库
- **pyscreeze>=0.1.21**: 屏幕截图库
- **pygetwindow>=0.0.5**: 窗口管理库
- **mouseinfo>=0.1.3**: 鼠标信息库
- **pyrect>=0.2.0**: 矩形操作库

## 配置说明

编辑 `config.json`:

```json
{
    "register_count": 5,
    "proxy": {
        "base_account": "admin_w1-zone-resi-region-us",
        "password": "WorldUsers135",
        "host": "7e61ce2c5694ec79.nbd.us.ip2world.vip",
        "port": 6001,
        "minute": 10,
        "states": ["california", "florida", "texas", "newyork", "georgia"],
        "cities": ["losangeles", "miami", "chicago", "houston", "atlanta"]
    },
    "bitbrowser_url": "http://127.0.0.1:54345",
    "output_file": "outlook_accounts.txt",
    "timeouts": {
        "page_load": 90000,
        "navigation": 90000,
        "element_wait": 90000
    },
    "debug": {
        "enabled": true,
        "screenshot_on_error": true,
        "verbose_logging": true
    },
    "captcha": {
        "auto_handle": true,
        "hold_duration_min": 3,
        "hold_duration_max": 5,
        "manual_fallback": true
    }
}
```

- `register_count`: 要注册的账号数量
- `proxy`: 代理服务器配置
  - `minute`: 会话时间（分钟），会拼接成 `-sessTime-10`
  - `states`: 可用的州名列表，用于生成代理账号
  - `cities`: 可用的城市名列表，用于生成代理账号
- `bitbrowser_url`: 比特浏览器API地址
- `output_file`: 账号信息保存文件
- `timeouts`: 超时配置（毫秒）
  - `page_load`: 页面加载超时时间（默认90秒）
  - `navigation`: 页面导航超时时间（默认90秒）
  - `element_wait`: 元素等待超时时间（默认90秒）
- `debug`: 调试配置
  - `enabled`: 启用调试模式
  - `screenshot_on_error`: 错误时自动截图
  - `verbose_logging`: 详细日志输出
- `captcha`: 人机验证配置
  - `auto_handle`: 自动处理验证码
  - `hold_duration_min`: 最小按住时间（秒）
  - `hold_duration_max`: 最大按住时间（秒）
  - `manual_fallback`: 自动失败时手动处理

## 使用方法

1. 确保比特浏览器已启动
2. 配置好代理信息
3. 运行程序：

```bash
python main.py
```

## 依赖安装

```bash
pip install requests playwright
```

## 输出文件

注册成功的账号信息会保存到 `outlook_accounts.txt`:

```
Email: <EMAIL>
Password: password123
First Name: John
Last Name: Doe
Registration Time: 2024-01-01 12:00:00
--------------------------------------------------
```

## 人性化输入特性

程序采用了多种技术来模拟真实用户行为：

- **逐字符输入**: 每个字符单独输入，模拟真实打字
- **随机延迟**: 字符间随机延迟0.05-0.3秒
- **随机行为**: 随机鼠标移动、页面滚动
- **自然停顿**: 操作间随机停顿0.5-2秒
- **智能用户名**: 生成符合Outlook要求的用户名（必须以字母开头）
- **智能选择器**: 自动尝试多种元素选择器，提高兼容性
- **智能生日选择**: 适应新的下拉按钮和输入框格式
- **人工点击**: 点击前后都有随机延迟

### 用户名生成规则

程序会自动生成符合Outlook要求的用户名：

- **必须以字母开头**: 符合Outlook的验证要求
- **长度8-15位**: 在合理范围内
- **多种模式**:
  - 模式1: 字母开头 + 随机字母数字组合
  - 模式2: 字母开头 + 纯字母 + 数字结尾
  - 模式3: 两个字母开头 + 数字 + 字母组合
- **示例**: `abc123def`, `john2024`, `xy789hello`

### 生日选择流程

程序按照以下精确步骤选择生日信息：

1. **月份选择**:
   ```python
   page.click("#BirthMonthDropdown")  # 点击月份下拉框
   page.get_by_role("option", name="January").click()  # 选择月份
   ```

2. **日期选择**:
   ```python
   page.click("#BirthDayDropdown")  # 点击日期下拉框
   page.get_by_role("option", name="15").click()  # 选择日期
   ```

3. **年份填写**:
   ```python
   page.fill("input[name='BirthYear']", "1998")  # 填写年份
   ```

- **月份映射**: 自动将数字月份转换为英文名称 (1→January, 2→February...)
- **备用方案**: 主方法失败时自动尝试备用选择器
- **随机延迟**: 每个操作间有0.5-1.0秒的随机延迟
- **智能点击**: 6种点击方法解决元素拦截问题

### 智能点击系统

程序使用多层次的点击策略来处理复杂的页面元素：

1. **普通点击**: `page.click(selector)`
2. **强制点击**: `page.click(selector, force=True)`
3. **Locator点击**: `page.locator(selector).click()`
4. **Locator强制点击**: `page.locator(selector).click(force=True)`
5. **位置点击**: `page.click(selector, position={"x": 50, "y": 15})`
6. **JavaScript点击**: `page.evaluate('document.querySelector(selector).click()')`

这个系统专门解决了以下问题：
- **Label元素拦截**: 当label覆盖在按钮上时
- **元素被覆盖**: 当其他元素遮挡目标元素时
- **元素不稳定**: 当元素状态变化时
- **定位问题**: 当元素不在可见区域时

## 人机验证处理

### 自动验证功能

程序能够自动检测和处理Outlook的人机验证：

1. **检测验证页面**:
   - 识别"Let's prove you're human"标题
   - 检测"Press and hold the button"提示
   - 找到验证iframe元素

2. **自动处理流程**:
   ```python
   # 检测验证页面
   if handle_human_verification(page, config):
       # 使用pyautogui处理
       pyautogui.moveTo(center_x, center_y)  # 移动到屏幕中心
       pyautogui.mouseDown()                 # 按下鼠标
       time.sleep(3-5)                       # 保持按住3-5秒
       pyautogui.mouseUp()                   # 释放鼠标
   ```

3. **多重保障**:
   - **自动处理**: 使用pyautogui模拟长按操作
   - **手动备用**: 自动失败时提示手动操作
   - **状态检测**: 验证完成后检查页面跳转
   - **调试支持**: 详细日志和截图记录

### 使用说明

1. **确保浏览器可见**: pyautogui需要在屏幕上找到验证按钮
2. **不要移动鼠标**: 验证过程中避免手动操作鼠标
3. **等待提示**: 程序会显示"准备使用pyautogui处理验证..."
4. **手动备用**: 如果自动处理失败，会提示手动完成

### 依赖安装

程序会自动检测并安装pyautogui：
```bash
pip install pyautogui
```

### 智能选择器系统

程序使用智能选择器系统来提高兼容性：

- **智能按钮点击**: 自动尝试多种按钮选择器
  - `button[data-testid="primaryButton"]` (主要)
  - `input[type="submit"]` (备用)
  - `button:has-text("Next")` (文本匹配)
  - 等多种备用方案

- **智能密码输入**: 自动尝试多种密码框选择器
  - `input[type="password"][autocomplete="new-password"]` (主要)
  - `input[type="password"]` (通用)
  - `input[name="Password"]` (名称匹配)
  - 等多种备用方案

- **智能生日选择**: 精确模仿用户操作，解决元素拦截问题
- **人机验证处理**: 使用pyautogui自动处理"Press and hold"验证
  - `page.click("#BirthMonthDropdown")` (点击月份下拉框)
  - `page.get_by_role("option", name="January").click()` (选择月份)
  - `page.click("#BirthDayDropdown")` (点击日期下拉框)
  - `page.get_by_role("option", name="15").click()` (选择日期)
  - `page.fill("input[name='BirthYear']", "1998")` (填写年份)

## 错误处理和调试

### 增强的错误处理

程序现在包含强大的错误处理机制：

- **重试机制**: 所有关键操作都有3次重试机会
- **智能等待**: 多种页面加载等待策略
- **错误恢复**: 主选择器失败时自动尝试备用方案
- **详细日志**: 显示每次操作的详细信息

### 调试功能

启用调试模式后，程序会：

- **详细日志**: 显示每个步骤的详细执行信息
- **自动截图**: 在错误发生时自动保存页面截图
- **状态监控**: 实时显示页面URL和元素状态
- **性能监控**: 显示每个操作的耗时

### 常见问题解决

1. **超时错误**:
   - 增加配置文件中的超时时间
   - 检查网络连接稳定性
   - 启用调试模式查看详细信息

2. **元素找不到**:
   - 程序会自动尝试多种选择器
   - 查看调试截图了解页面状态
   - 检查页面是否完全加载

3. **点击失败**:
   - 程序会自动重试3次
   - 尝试多种按钮选择器
   - 查看详细日志了解失败原因

## 超时配置说明

可以通过修改 `config.json` 中的 `timeouts` 配置来调整各种超时时间：

- **page_load**: 页面加载超时（如 `page.goto()` 操作）
- **navigation**: 页面导航超时（如等待页面加载完成）
- **element_wait**: 元素等待超时（如等待按钮可见）

如果遇到 "Timeout exceeded" 错误，可以适当增加相应的超时时间。

## 注意事项

- 确保比特浏览器运行在配置的端口
- 确保代理服务可用
- 注册间隔30-60秒，避免频率过高
- 可能遇到人机验证需要手动处理
- 人性化输入会增加注册时间，但提高成功率
- 网络较慢时可以增加超时时间配置
